
## Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **SQLAlchemy** - Database ORM with SQLite
- **Google Gemini 2.5** - Advanced multimodal AI for video understanding
- **ChromaDB** - Vector database for semantic search
- **FFmpeg** - Video processing and frame extraction
- **yt-dlp** - YouTube video downloading

### Frontend
- **React + TypeScript** - Modern web interface
- **Tailwind CSS** - Utility-first styling
- **Vite** - Fast development and build tool
- **Lucide React** - Beautiful icons

## Prerequisites

- Python 3.8+
- Node.js 16+
- FFmpeg installed
- Google Gemini API key




<img width="1377" alt="Screenshot 2025-06-05 at 7 39 25 PM" src="https://github.com/user-attachments/assets/111b35c4-3cfb-4ebc-82d4-0d17d00f8e12" />
